<template>
  <!-- 价量分析页面 -->
  <div class="cw-jlfx-page">
    <a-card :bordered="false">
      <template #title>
        金属利润影响分析 (瀑布图)
      </template>
      <template #extra>
        <a-radio-group v-model:value="chartType" button-style="solid" size="middle">
          <a-radio-button value="price">价格影响</a-radio-button>
          <a-radio-button value="volume">产量影响</a-radio-button>
        </a-radio-group>
      </template>
      <!-- 查询条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension" @change="fetchBarData">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="pickerType"
          :allowClear="false"
          @change="fetchBarData"
        />
      </a-space>

      <!-- 影响概览卡片 -->
      <div class="impact-summary">
        <!-- 价格影响列表 -->
        <div class="impact-item price" :class="{ active: chartType === 'price' }">
          <div class="label">价格影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="p in priceImpactList" :key="p.metal">
              <span class="metal">{{ p.metal }}</span>
              <span :class="p.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(p.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalPriceImpact) }}</div>
        </div>
        <!-- 产量影响列表 -->
        <div class="impact-item volume" :class="{ active: chartType === 'volume' }">
          <div class="label">产量影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="v in volumeImpactList" :key="v.metal">
              <span class="metal">{{ v.metal }}</span>
              <span :class="v.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(v.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalVolumeImpact) }}</div>
        </div>
      </div>

      <div ref="barRef" class="bar-chart"></div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-price-volume-statistics">
  // 价量分析脚本
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { metalProfitBar } from '/@/api/cw/statistics';
  // 工具
  import { useECharts } from '/@/hooks/web/useECharts';
  import { formatNumber } from '/@/utils/showUtils';

  /** 查询条件 */
  const dimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const pickerType = computed(() => dimension.value);

  /** 图表类型切换 */
  const chartType = ref<'price' | 'volume'>('price');

  /** 图表实例 */
  const barRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setBarOptions } = useECharts(barRef as any);

  /** 数据源 */
  const barList = ref<any[]>([]);

  /** 影响汇总 */
  const totalPriceImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.priceImpact ?? 0), 0);
  });
  const totalVolumeImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.volumeImpact ?? 0), 0);
  });

  /** 分金属影响列表 */
  const priceImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.priceImpact ?? 0) }));
  });
  const volumeImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.volumeImpact ?? 0) }));
  });

  /** 监听变化自动刷新 */
  watch([dimension, barDate], () => fetchBarData());
  watch(chartType, () => updateWaterfallChart());

  onMounted(() => {
    fetchBarData();
  });

  /** 获取柱状图数据 */
  async function fetchBarData() {
    try {
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await metalProfitBar({ date: dateStr, dimension: dimension.value });
      updateWaterfallChart();
    } catch (e) {
      console.error(e);
      message.error('金属利润数据获取失败');
    }
  }

  /** 更新瀑布图 */
  function updateWaterfallChart() {
    if (!barList.value?.length) return;

    const metals = barList.value.map((d) => d.metal || '--');
    
    if (chartType.value === 'price') {
      // 价格影响瀑布图
      const priceImpacts = barList.value.map((d) => Number(d.priceImpact ?? 0));
      const waterfallData = generateWaterfallData(metals, priceImpacts);
      
      setBarOptions({
        title: {
          text: '价格影响瀑布图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#1890ff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: (params: any) => {
            const p = params as any[];
            if (!p?.length) return '';
            const data = p.find(item => item.seriesName !== '基础') || p[0];
            const value = data.data?.value ?? data.value;
            return `${data.name}<br/>影响值：${formatNumber(value)}<br/>累计：${formatNumber(data.data.cumulative)}`;
          },
        },
        grid: { left: 80, right: 40, bottom: 60, top: 60, containLabel: true },
        xAxis: { 
          type: 'category', 
          data: waterfallData.map(d => d.name),
          axisLabel: { rotate: 45 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { formatter: (v: any) => formatNumber(v) },
          name: '影响金额 (万元)',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [
          {
            name: '基础',
            type: 'bar',
            stack: 'waterfall',
            barWidth: '60%',
            itemStyle: { color: 'transparent' },
            data: waterfallData.map((d, index) => ({
              value: d.startValue,
              cumulative: d.cumulative
            }))
          },
          {
            name: '价格影响',
            type: 'bar',
            stack: 'waterfall',
            barWidth: '60%',
            itemStyle: {
              color: (param: any) => {
                const value = param.data.value;
                if (value >= 0) return '#ff7875'; // 红色表示正向影响
                return '#95de64'; // 绿色表示负向影响
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: (param: any) => {
                const value = param.data.value;
                return value >= 0 ? `+${formatNumber(value)}` : formatNumber(value);
              },
              fontSize: 12
            },
            data: waterfallData.map((d, index) => ({
              value: d.value, // 保持原始值，让ECharts自然处理正负堆叠
              cumulative: d.cumulative,
              itemStyle: {
                color: d.value >= 0 ? '#ff7875' : '#95de64'
              }
            }))
          }
        ]
      } as any);
    } else {
      // 产量影响瀑布图
      const volumeImpacts = barList.value.map((d) => Number(d.volumeImpact ?? 0));
      const waterfallData = generateWaterfallData(metals, volumeImpacts);
      
      setBarOptions({
        title: {
          text: '产量影响瀑布图',
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold',
            color: '#52c41a'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: (params: any) => {
            const p = params as any[];
            if (!p?.length) return '';
            const data = p.find(item => item.seriesName !== '基础') || p[0];
            const value = data.data?.value ?? data.value;
            return `${data.name}<br/>影响值：${formatNumber(value)}<br/>累计：${formatNumber(data.data.cumulative)}`;
          },
        },
        grid: { left: 80, right: 40, bottom: 60, top: 60, containLabel: true },
        xAxis: { 
          type: 'category', 
          data: waterfallData.map(d => d.name),
          axisLabel: { rotate: 45 }
        },
        yAxis: {
          type: 'value',
          axisLabel: { formatter: (v: any) => formatNumber(v) },
          name: '影响金额 (万元)',
          nameLocation: 'middle',
          nameGap: 40
        },
        series: [
          {
            name: '基础',
            type: 'bar',
            stack: 'waterfall',
            barWidth: '60%',
            itemStyle: { color: 'transparent' },
            data: waterfallData.map((d, index) => ({
              value: d.startValue,
              cumulative: d.cumulative
            }))
          },
          {
            name: '产量影响',
            type: 'bar',
            stack: 'waterfall',
            barWidth: '60%',
            itemStyle: {
              color: (param: any) => {
                const value = param.data.value;
                if (value >= 0) return '#ff7875'; // 红色表示正向影响
                return '#95de64'; // 绿色表示负向影响
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: (param: any) => {
                const value = param.data.value;
                return value >= 0 ? `+${formatNumber(value)}` : formatNumber(value);
              },
              fontSize: 12
            },
            data: waterfallData.map((d, index) => ({
              value: d.value, // 保持原始值，让ECharts自然处理正负堆叠
              cumulative: d.cumulative,
              itemStyle: {
                color: d.value >= 0 ? '#ff7875' : '#95de64'
              }
            }))
          }
        ]
      } as any);
    }
  }

  /** 生成瀑布图数据 */
  function generateWaterfallData(names: string[], values: number[]) {
    let cumulative = 0;
    return names.map((name, index) => {
      const value = values[index];
      const startValue = cumulative; // 基础系列始终从前一个累计值开始
      cumulative += value;

      return {
        name,
        value, // 保持原始值，让ECharts自然处理正负堆叠
        startValue,
        cumulative
      };
    });
  }
</script>

<style scoped lang="less">
.cw-jlfx-page {
  .ant-card {
    margin-bottom: 16px;
    .bar-chart {
      width: 100%;
      height: calc(100vh - 200px); // 视口自适应高度
    }

    /* ==== Impact Cards Modern Style ==== */
    .impact-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin: 12px 0;
    }

    .impact-item {
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 16px 20px;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &.active {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        transform: translateY(-2px);
      }

      &.price.active {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      &.volume.active {
        border-color: #52c41a;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
      }
    }

    .impact-item .label {
      font-size: 16px;
      font-weight: 600;
      color: #595959;
      margin-bottom: 8px;
    }

    .impact-list {
      flex: 1;
      border-top: 1px dashed #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }

    .impact-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 4px; /* 增加左右间隙 */
      font-size: 14px;
    }

    .impact-row:not(:last-child) {
      border-bottom: 1px dashed #f0f0f0;
    }

    .impact-row .metal {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .impact-row .metal::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #1890ff;
    }

    .impact-row .pos {
      color: #f5222d;
    }
    .impact-row .neg {
      color: #52c41a;
    }

    .total {
      margin-top: 10px;
      font-weight: 600;
      text-align: right;
      font-size: 18px; /* 更突出 */
    }
  }
}
</style>
